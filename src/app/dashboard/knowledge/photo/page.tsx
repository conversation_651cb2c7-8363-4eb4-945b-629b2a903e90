'use client'

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import Link from 'next/link'
import NextImage from 'next/image'
import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import DashboardFooter from '@/components/DashboardFooter'
import { FaBrain, FaListAlt, FaImage, FaTrash, FaYoutube, FaTelegram } from 'react-icons/fa';
import { Button } from '@/components/ui/Button';
import { LinkButton, DashboardHeader } from '@/components/ui';
import KnowledgeTopSection from '@/components/ui/knowledge/KnowledgeTopSection';
import { UpdateStatusOverlay } from '@/components/ui/knowledge';
import PaginationControls from '@/components/ui/knowledge/PaginationControls';
import { 
  CancelConfirmationModal, 
  UpdateConfirmationModal, 
  DeleteConfirmationModal, 
  ImageGalleryModal,
  PhotoCannotDeleteModal,
  PhotoRemoveConfirmationModal 
} from '@/components/ui/modals';
import { ImageUploadArea, PhotoIdInput, FormActions } from '@/components/ui/forms';

import { useKnowledgeData } from '@/hooks/useOptimizedData'
import imageCompression from 'browser-image-compression'
import { v4 as uuidv4 } from 'uuid'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonElement} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

// Photo type definition
type Photo = {
  id: string;
  photo_id: string;
  photo_url: string;
  photo_file_path: string;
}

export default function PhotoPage() {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use unified knowledge cache for all photo page data with local state mutations
  const { 
    data: knowledgeData, 
    loading: isKnowledgeLoading, 
    refetch: refetchKnowledgeData,
    addPhotoToState,
    updatePhotoInState, 
    removePhotoFromState
  } = useKnowledgeData()
  const clientLang = knowledgeData?.clientLang
  const knowledgeStats = knowledgeData?.knowledgeStats
  const photosData = useMemo(() => knowledgeData?.photos || [], [knowledgeData?.photos])

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isKnowledgeLoading

  // Legacy stats state (keeping for compatibility)
  const [businessInsightCount, setBusinessInsightCount] = useState(0)
  const [productListCount, setProductListCount] = useState(0)
  const [productListLimit, setProductListLimit] = useState<number>(1)

  // Photo catalog state
  const [photos, setPhotos] = useState<Array<any>>([])
  const [isLoadingPhotos, setIsLoadingPhotos] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  // Derived state using useMemo to prevent infinite re-renders
  const filteredPhotos = useMemo(() => {
    if (!searchQuery.trim()) return photos;
    return photos.filter(photo =>
      photo.photo_id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [photos, searchQuery]);

  const totalPages = useMemo(() => {
    return Math.ceil(filteredPhotos.length / itemsPerPage);
  }, [filteredPhotos.length, itemsPerPage]);

  // Photo upload state
  const [isUploading, setIsUploading] = useState(false)
  const [photoId, setPhotoId] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploadError, setUploadError] = useState('')
  const [isProcessingImage, setIsProcessingImage] = useState(false)
  const [imagePreviews, setImagePreviews] = useState<Array<{id: string, url: string}>>([])
  const dropAreaRef = useRef<HTMLDivElement>(null)

  // Photo ID validation state
  const [isCheckingPhotoId, setIsCheckingPhotoId] = useState(false)
  const [photoIdExists, setPhotoIdExists] = useState(false)
  const [photoIdValidationMessage, setPhotoIdValidationMessage] = useState('')
  const [photoIdCheckTimeout, setPhotoIdCheckTimeout] = useState<NodeJS.Timeout | null>(null)

  // Photo update state
  const [showUpdateForm, setShowUpdateForm] = useState(false)
  const [photoToUpdate, setPhotoToUpdate] = useState<any>(null)
  const [updatePhotoId, setUpdatePhotoId] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateError, setUpdateError] = useState('')
  const [updateSelectedFiles, setUpdateSelectedFiles] = useState<File[]>([])
  const [updateImagePreviews, setUpdateImagePreviews] = useState<Array<{id: string, url: string, isExisting: boolean, url_index?: number}>>([])
  const updateDropAreaRef = useRef<HTMLDivElement>(null)

  // Confirmation modals
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const [showUploadConfirm, setShowUploadConfirm] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showUpdateConfirm, setShowUpdateConfirm] = useState(false)
  const [showCancelUpdateConfirm, setShowCancelUpdateConfirm] = useState(false)
  const [photoToDelete, setPhotoToDelete] = useState<any>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteSuccess, setDeleteSuccess] = useState(false)

  // Photo removal confirmation
  const [showRemovePhotoConfirm, setShowRemovePhotoConfirm] = useState(false)
  const [photoIndexToRemove, setPhotoIndexToRemove] = useState<number | null>(null)
  const [isRemovingFromUpdate, setIsRemovingFromUpdate] = useState(false)

  // Cannot delete photo modal (when photo is linked to FAQs)
  const [showCannotDeleteModal, setShowCannotDeleteModal] = useState(false)
  const [linkedQuestions, setLinkedQuestions] = useState<string[]>([])

  // Status overlay for upload progress
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)

  // Image preview state
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [previewImages, setPreviewImages] = useState<string[]>([])
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Note: filterPhotos function removed - filtering now handled by useMemo above

  // Single focused useEffect for data syncing only
  useEffect(() => {
    if (!photosData) return;

    setIsLoadingPhotos(true);
    
    // Update base photos (derived state will auto-update via useMemo)
    setPhotos(photosData);
    
    // Reset to first page when data changes
    setCurrentPage(1);
    setIsLoadingPhotos(false);
  }, [photosData]);

  // Reset to first page if current page would be out of bounds when pagination changes
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage]);

  // Handle search input change with debounce
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  }, []);

  // Get current page items - memoize to prevent unnecessary recalculations
  const getCurrentPageItems = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredPhotos.slice(startIndex, endIndex);
  }, [currentPage, filteredPhotos, itemsPerPage]);

  // Check if photo ID exists in local photos array
  const checkPhotoIdExists = useCallback((id: string): boolean => {
    if (!id.trim()) {
      return false;
    }

    // Capitalize the ID before checking since all IDs in database are capitalized
    const capitalizedId = id.trim().toUpperCase();

    // Check against current photos state
    return photos.some(photo => photo.photo_id === capitalizedId);
  }, [photos]); // Include photos dependency to get current state

  // Debounced photo ID validation
  const validatePhotoId = useCallback((id: string) => {
    // Clear any existing timeout
    setPhotoIdCheckTimeout(prevTimeout => {
      if (prevTimeout) {
        clearTimeout(prevTimeout);
      }
      return null;
    });

    // Reset validation state
    setPhotoIdValidationMessage('');
    setPhotoIdExists(false);

    if (!id.trim()) {
      return;
    }

    // Set loading state
    setIsCheckingPhotoId(true);

    // Set a new timeout for debounced checking
    const timeoutId = setTimeout(() => {
      try {
        const exists = checkPhotoIdExists(id);
        setPhotoIdExists(exists);

        if (exists) {
          setPhotoIdValidationMessage(t('photo_id_exists') || 'Photo ID already exists');
        } else {
          setPhotoIdValidationMessage('');
        }
      } catch (error) {
        console.error('Error validating photo ID:', error);
        setPhotoIdValidationMessage('');
      } finally {
        setIsCheckingPhotoId(false);
      }
    }, 500); // 500ms debounce

    setPhotoIdCheckTimeout(timeoutId);
  }, [checkPhotoIdExists, t]);


  // Validate file
  const validateFile = (file: File): string | null => {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return t('file_size_error').replace('{size}', (file.size / (1024 * 1024)).toFixed(2));
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      return t('file_type_error');
    }

    return null;
  };

  // Handle image compression and processing
  const processImage = async (file: File): Promise<{ compressedFile: File }> => {
    // Compression options with consistent settings (same as productList)
    const options = {
      maxSizeMB: 0.7,               // Limit file size to 700KB
      maxWidthOrHeight: 1080,       // Resize to max 1080 width/height
      useWebWorker: true,           // Use web worker for better performance
      fileType: 'image/jpeg',       // Always convert to JPEG
      initialQuality: 0.8,          // High quality (80%)
      alwaysKeepResolution: true    // Maintain aspect ratio
    };

    try {

      // Compress the image
      const compressedBlob = await imageCompression(file, options);

      // Create a consistent filename with timestamp to avoid collisions
      const timestamp = Date.now();
      const baseFileName = file.name.split('.')[0].replace(/[^a-zA-Z0-9]/g, '_');
      const fileName = `${baseFileName}_${timestamp}.jpg`;

      // Create a proper File object from the blob
      const compressedFile = new File([compressedBlob], fileName, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      return { compressedFile };

    } catch (error) {
      console.error('Image compression failed:', error);
      throw error;
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUploadError('');

    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const currentCount = selectedFiles.length;
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUploadError(t('remaining_slots_error').replace('{count}', remainingSlots.toString()));
        // Process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleImageFile(file));
      } else {
        files.forEach(file => handleImageFile(file));
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  // Handle single image file
  const handleImageFile = async (file: File) => {
    // Validate file
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }

    try {
      setIsProcessingImage(true);

      // Step 1: Compress the image
      const { compressedFile } = await processImage(file);

      // Step 2: Create a preview from the compressed file
      const previewUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Step 3: Update state with the compressed file
      setSelectedFiles(prev => [...prev, compressedFile]);
      setImagePreviews(prev => [...prev, {
        id: `preview-${uuidv4()}`,
        url: previewUrl
      }]);

    } catch (error) {
      console.error('Error processing image:', error);
      setUploadError(t('process_error'));
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Show confirmation before removing a file
  const showRemoveFileConfirm = (index: number, isUpdate: boolean = false) => {
    setPhotoIndexToRemove(index);
    setIsRemovingFromUpdate(isUpdate);
    setShowRemovePhotoConfirm(true);
  };

  // Confirm and remove a file from the selection
  const confirmRemoveFile = () => {
    if (photoIndexToRemove === null) return;

    if (isRemovingFromUpdate) {
      // Remove from update form
      const preview = updateImagePreviews[photoIndexToRemove];

      if (preview.isExisting) {
        // Remove existing image
        setUpdateImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      } else {
        // Find the corresponding new file index
        const newFileIndex = updateImagePreviews.filter(p => p.isExisting).length;
        const fileIndexToRemove = photoIndexToRemove - newFileIndex;

        // Remove new image
        setUpdateSelectedFiles(prev => prev.filter((_, i) => i !== fileIndexToRemove));
        setUpdateImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      }
    } else {
      // Remove from upload form
      setSelectedFiles(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      setImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
    }

    // Reset state
    setShowRemovePhotoConfirm(false);
    setPhotoIndexToRemove(null);
    setIsRemovingFromUpdate(false);
  };

  // Cancel remove file
  const cancelRemoveFile = () => {
    setShowRemovePhotoConfirm(false);
    setPhotoIndexToRemove(null);
    setIsRemovingFromUpdate(false);
  };

  // Legacy remove file function (now just calls the confirmation)
  const removeFile = (index: number) => {
    showRemoveFileConfirm(index, false);
  };

  // Handle photo ID input with real-time validation
  const handlePhotoIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhotoId(value);

    // Clear upload error when user starts typing
    if (uploadError) {
      setUploadError('');
    }

    // Trigger real-time validation
    validatePhotoId(value);
  };

  // Handle photo ID blur (for capitalization only)
  const handlePhotoIdBlur = () => {
    if (photoId.trim()) {
      const capitalizedId = photoId.trim().toUpperCase();
      // Only update the input field if capitalization changed it
      if (capitalizedId !== photoId) {
        setPhotoId(capitalizedId);
        // No re-validation needed - real-time validation already handled it
      }
    }
  };

  // Handle form submission - Show confirmation first
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedFiles.length === 0 || !photoId.trim()) {
      setUploadError(t('select_file_id_error'));
      return;
    }

    // Check if photo ID already exists
    if (photoIdExists) {
      setUploadError(t('photo_id_exists') || 'Photo ID already exists');
      return;
    }

    // Double-check photo ID existence before proceeding
    const exists = checkPhotoIdExists(photoId.trim());
    if (exists) {
      setPhotoIdExists(true);
      setPhotoIdValidationMessage(t('photo_id_exists') || 'Photo ID already exists');
      setUploadError(t('photo_id_exists') || 'Photo ID already exists');
      return;
    }

    // Check if we've reached the photo limit
    // Note: We create 1 photo record with multiple files, not multiple records
    if (photoCount + 1 > photoLimit) {
      setUploadError(t('photo_limit_error').replace('{count}', (photoLimit - photoCount).toString()));
      return;
    }

    // Show confirmation dialog
    setShowUploadConfirm(true);
  };

  // Actual upload after confirmation
  const handleConfirmedUpload = async () => {
    setShowUploadConfirm(false);
    setIsUploading(true);
    setUpdateStatus('loading');
    setUpdateMessage(t('preparing_upload'));
    setUpdateProgress(10);

    try {
      setIsProcessingImage(true);

      // Arrays to store URLs and file paths
      let photo_urls: string[] = [];
      let photo_file_paths: string[] = [];

      setUpdateMessage(t('uploading_photos'));
      setUpdateProgress(20);

      // Upload files to R2 via API
      
      // Create FormData for file upload
      const formData = new FormData();
      selectedFiles.forEach((file, index) => {
        formData.append('files', file);
      });
      formData.append('photoId', photoId);

      // Upload to R2
      const uploadResponse = await fetch('/api/file/photos', {
        method: 'POST',
        body: formData
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        if (uploadResponse.status === 401) {
          throw new Error(t('auth_error') || 'Authentication required');
        }
        throw new Error(errorData.error || t('upload_error'));
      }

      const uploadResult = await uploadResponse.json();
      
      // Get URLs and paths from R2 response
      photo_urls = uploadResult.photoUrls || [];
      photo_file_paths = uploadResult.filePaths || [];

      // Update progress after upload completion
      setUpdateProgress(60);

      setUpdateMessage(t('saving_photo_info'));
      setUpdateProgress(80);

      // Save photo via API
      const insertResponse = await fetch('/api/knowledge/photos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo_id: photoId,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
      });

      const insertResult = await insertResponse.json();

      if (!insertResponse.ok) {
        if (insertResponse.status === 401) {
          throw new Error(t('auth_error') || 'Authentication required');
        }
        throw new Error(insertResult.error || t('save_error').replace('{message}', 'Failed to save photo'));
      }

      setUpdateProgress(100);

      // Update local state immediately for instant UI feedback
      const newPhoto = {
        id: insertResult.body?.id || Date.now(), // Use returned ID or fallback
        photo_id: photoId,
        photo_url: photo_urls,
        photo_file_path: photo_file_paths,
        updated_at: new Date().toISOString()
      };
      
      // Add photo to local state instantly (no server refetch needed)
      addPhotoToState(newPhoto);

      // Reset form
      setPhotoId('');
      setSelectedFiles([]);
      setImagePreviews([]);
      setShowUploadForm(false);

      // Show success message
      setUpdateStatus('success');
      setUpdateMessage(t('photos_uploaded'));

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUploading(false);
      }, 1500);

    } catch (error) {
      console.error('Error uploading photos:', error);
      setUpdateStatus('error');
      setUpdateMessage(`Error uploading photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsUploading(false);
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Handle cancel button click
  const handleCancel = () => {
    // If there are photos, show confirmation dialog
    if (selectedFiles.length > 0) {
      setShowCancelConfirm(true);
    } else {
      // Otherwise just close the form
      closeUploadForm();
    }
  };

  // Confirm cancel and close form
  const confirmCancel = () => {
    setShowCancelConfirm(false);
    closeUploadForm();

    // If we have a photo to update (from edit button click), proceed with edit operation
    if (photoToUpdate) {
      const photo = photoToUpdate;

      // Reset photoToUpdate to avoid infinite loop
      setPhotoToUpdate(null);

      // Call setupEditForm to continue with the edit operation
      setTimeout(() => {
        setupEditForm(photo);
      }, 100);
    }
  };

  // Close upload form and reset state
  const closeUploadForm = () => {
    setShowUploadForm(false);
    setPhotoId('');
    setSelectedFiles([]);
    setImagePreviews([]);
    setUploadError('');

    // Reset photo ID validation state
    setIsCheckingPhotoId(false);
    setPhotoIdExists(false);
    setPhotoIdValidationMessage('');

    // Clear any pending timeout
    if (photoIdCheckTimeout) {
      clearTimeout(photoIdCheckTimeout);
      setPhotoIdCheckTimeout(null);
    }
  };

  // Toggle upload form
  const toggleUploadForm = () => {
    // If update form is open, check for changes
    if (showUpdateForm) {
      if (hasUpdateFormChanges()) {
        // Store a flag to indicate we want to show the upload form after discarding changes
        sessionStorage.setItem('showUploadFormAfterDiscard', 'true');
        setShowCancelUpdateConfirm(true);
      } else {
        closeUpdateForm();
        setShowUploadForm(true);
      }
      return;
    }

    // Handle upload form toggle
    if (showUploadForm) {
      // If form is open, handle cancel logic
      handleCancel();
    } else {
      // If form is closed, just open it
      setShowUploadForm(true);

      // Reset photoToUpdate to avoid confusion when switching from edit to add mode
      setPhotoToUpdate(null);
    }
  };





  // Disable page scroll when gallery is open
  useEffect(() => {
    if (previewImage) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [previewImage]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = showCancelConfirm || showUploadConfirm || showDeleteConfirm ||
                       showUpdateConfirm || showCancelUpdateConfirm || showRemovePhotoConfirm ||
                       showCannotDeleteModal || updateStatus === 'loading' || updateStatus === 'success' ||
                       updateStatus === 'error';

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [showCancelConfirm, showUploadConfirm, showDeleteConfirm, showUpdateConfirm,
      showCancelUpdateConfirm, showRemovePhotoConfirm, showCannotDeleteModal, updateStatus]);

  // Enable drag and drop for image upload
  useEffect(() => {
    const dropArea = dropAreaRef.current;
    if (!dropArea) return;

    const highlight = () => dropArea.classList.add('border-jade-purple');
    const unhighlight = () => dropArea.classList.remove('border-jade-purple');

    const preventDefaults = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = async (e: DragEvent) => {
      unhighlight();
      preventDefaults(e);

      if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files);
        const currentCount = selectedFiles.length;
        const remainingSlots = 4 - currentCount;

        if (files.length > remainingSlots) {
          setUploadError(`You can only add ${remainingSlots} more image(s).`);
          // Process only the allowed number of files
          files.slice(0, remainingSlots).forEach(file => handleImageFile(file));
        } else {
          files.forEach(file => handleImageFile(file));
        }
      }
    };

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, unhighlight, false);
    });

    dropArea.addEventListener('drop', handleDrop, false);

    return () => {
      if (dropArea) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, preventDefaults);
        });

        ['dragenter', 'dragover'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, highlight);
        });

        ['dragleave', 'drop'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, unhighlight);
        });

        dropArea.removeEventListener('drop', handleDrop);
      }
    };
  }, []);

  // Note: handleSearch function removed - search is now handled by useEffect when searchQuery changes

  // Note: updateTotalPages function removed - now calculated inline to prevent dependency issues

  // Handle pagination
  const handlePageClick = (page: number) => {
    setCurrentPage(page)
  }


  // Handle delete photo
  const handleDeleteClick = (photo: any) => {
    setPhotoToDelete(photo);
    setShowDeleteConfirm(true);
    setIsDeleting(false);
    // Reset delete success state is handled by modal component
  };

  // Confirm delete photo
  const confirmDeletePhoto = async () => {
    if (!photoToDelete) return;

    setIsDeleting(true);

    try {
      // Prepare file paths for deletion
      const filesToDelete = Array.isArray(photoToDelete.photo_file_path) 
        ? photoToDelete.photo_file_path 
        : [photoToDelete.photo_file_path];

      // Call complete delete API
      const deleteResponse = await fetch('/api/knowledge/photos', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo_id: photoToDelete.photo_id,
          file_paths: filesToDelete
        })
      });

      const deleteResult = await deleteResponse.json();

      if (!deleteResponse.ok) {
        if (deleteResponse.status === 401) {
          throw new Error(t('auth_error') || 'Authentication required');
        }
        throw new Error(`Error deleting photo: ${deleteResult.error_msg || 'Unknown error'}`);
      }

      // Handle response based on success/dependency status
      if (deleteResult.success) {
        // Success - close modal first, then show success overlay
        setIsDeleting(false);
        setDeleteSuccess(true);
        
        // Remove photo from local state instantly (no server refetch needed)
        removePhotoFromState(photoToDelete.photo_id);
        
        // Close modal immediately to show success overlay
        setShowDeleteConfirm(false);
        setPhotoToDelete(null);
        setDeleteSuccess(false);
        
        // Show success overlay after modal closes
        setTimeout(() => {
          setUpdateStatus('success');
          setUpdateMessage(t('delete_success_message') || 'Photo deleted successfully');
          
          // Auto-dismiss success overlay
          setTimeout(() => {
            setUpdateStatus('idle');
          }, 1500);
        }, 100);

      } else if (deleteResult.dependency) {
        // Dependency conflict - show cannot delete modal
        const userLang = clientLang || 'en';
        
        // Extract questions - always use question_p only, never fall back to question
        // Handle both single object and array responses
        const faqData = Array.isArray(deleteResult.body) ? deleteResult.body : [deleteResult.body];
        const questions = faqData
          .map((faq: any) => {
            const questionText = faq.question_p;
            return questionText && questionText.length > 30 ? `${questionText.substring(0, 30)}...` : questionText;
          })
          .filter(Boolean);

        setLinkedQuestions(questions);
        setShowCannotDeleteModal(true);
        setShowDeleteConfirm(false);
        setIsDeleting(false);

      } else {
        // Other error
        throw new Error(deleteResult.error_msg || 'Failed to delete photo');
      }

    } catch (error) {
      console.error('Error deleting photo:', error);
      alert(`Error deleting photo: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsDeleting(false);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setPhotoToDelete(null);
    setDeleteSuccess(false);
  };

  // Function to set up the edit form with photo data
  const setupEditForm = (photo: any) => {
    setPhotoToUpdate(photo);
    setUpdatePhotoId(photo.photo_id);
    setShowUpdateForm(true);
    setUpdateError('');
    setIsUpdating(false);
    setUpdateSelectedFiles([]);

    // Initialize image previews from existing photos
    const existingUrls = Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url];
    const previews = existingUrls.map((url: string, index: number) => ({
      id: `existing-${index}`,
      url: url,
      isExisting: true,
      url_index: index
    }));

    setUpdateImagePreviews(previews);

    // Hide the upload form if it's open
    setShowUploadForm(false);

    // Smooth scroll to the update form
    setTimeout(() => {
      const updateForm = document.querySelector('.update-form-container');
      if (updateForm) {
        updateForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Handle edit button click
  const handleEditClick = (photo: any) => {
    // Check if upload form is open and has changes
    if (showUploadForm && selectedFiles.length > 0) {
      // Show confirmation dialog
      setShowCancelConfirm(true);

      // Store the photo to update for after confirmation
      setPhotoToUpdate(photo);
      return;
    }

    // Proceed with edit operation
    setupEditForm(photo);
  };

  // Photo ID is now read-only, so we don't need a change handler

  // Handle file selection for update
  const handleUpdateFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUpdateError('');

    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const currentCount = updateSelectedFiles.length + updateImagePreviews.filter(p => p.isExisting).length;
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUpdateError(t('remaining_slots_error').replace('{count}', remainingSlots.toString()));
        // Process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleUpdateImageFile(file));
      } else {
        files.forEach(file => handleUpdateImageFile(file));
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  // Handle single image file for update
  const handleUpdateImageFile = async (file: File) => {
    // Validate file
    const error = validateFile(file);
    if (error) {
      setUpdateError(error);
      return;
    }

    try {
      setIsProcessingImage(true);

      // Compress the image
      const { compressedFile } = await processImage(file);

      // Create a preview from the compressed file
      const previewUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Update state with the compressed file
      setUpdateSelectedFiles(prev => [...prev, compressedFile]);
      setUpdateImagePreviews(prev => [...prev, {
        id: `new-${uuidv4()}`,
        url: previewUrl,
        isExisting: false
      }]);

    } catch (error) {
      console.error('Error processing image:', error);
      setUpdateError(t('process_error'));
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Remove a file from the update selection (now just calls the confirmation)
  const removeUpdateFile = (index: number) => {
    showRemoveFileConfirm(index, true);
  };

  // Check if update form has changes
  const hasUpdateFormChanges = () => {
    if (!photoToUpdate) return false;

    // Check if photos were added or removed
    const originalPhotoCount = Array.isArray(photoToUpdate.photo_url)
      ? photoToUpdate.photo_url.length
      : (photoToUpdate.photo_url ? 1 : 0);

    if (updateImagePreviews.length !== originalPhotoCount) return true;

    // Check if any new photos were added
    if (updateSelectedFiles.length > 0) return true;

    // If we got here, nothing changed
    return false;
  };

  // Show cancel update confirmation
  const showCancelUpdateConfirmation = () => {
    if (hasUpdateFormChanges()) {
      setShowCancelUpdateConfirm(true);
    } else {
      // No changes, just close the form
      closeUpdateForm();
    }
  };

  // Close update form without confirmation
  const closeUpdateForm = () => {
    setShowUpdateForm(false);
    setPhotoToUpdate(null);
    setUpdatePhotoId('');
    setUpdateSelectedFiles([]);
    setUpdateImagePreviews([]);
    setUpdateError('');
  };

  // Cancel update
  const handleCancelUpdate = () => {
    showCancelUpdateConfirmation();
  };

  // Show update confirmation
  const showUpdateConfirmation = (e: React.FormEvent) => {
    e.preventDefault();

    if (!updatePhotoId.trim()) {
      setUpdateError(t('select_file_id_error'));
      return;
    }

    if (updateImagePreviews.length === 0) {
      setUpdateError(t('select_file_id_error'));
      return;
    }

    setShowUpdateConfirm(true);
  };

  // Delete photos from storage
  const deletePhotosFromStorage = async (filePaths: string[]) => {
    try {
      const deleteResponse = await fetch('/api/file/photos', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filePaths })
      });

      if (!deleteResponse.ok) {
        console.error('Error deleting old photos from R2');
      }
    } catch (error) {
      console.error('Error in deletePhotosFromStorage:', error);
    }
  };

  // Handle update form submission
  const handleUpdateSubmit = async () => {
    setShowUpdateConfirm(false);
    setIsUpdating(true);

    // Show progress bar
    setUpdateStatus('loading');
    setUpdateMessage('Preparing to update photo...');
    setUpdateProgress(10);

    try {
      // Arrays to store URLs and file paths
      const photo_urls: string[] = [];
      const photo_file_paths: string[] = [];

      // Track which files to delete
      const filesToDelete = [...photoToUpdate.photo_file_path];

      // 1. Keep existing files that weren't removed
      setUpdateProgress(20);
      setUpdateMessage('Processing existing photos...');

      const existingPreviews = updateImagePreviews.filter(p => p.isExisting);
      for (const preview of existingPreviews) {
        if (preview.url_index !== undefined && Array.isArray(photoToUpdate.photo_url)) {
          photo_urls.push(photoToUpdate.photo_url[preview.url_index]);
          photo_file_paths.push(photoToUpdate.photo_file_path[preview.url_index]);
          // Remove this file from the delete list since we're keeping it
          const index = filesToDelete.indexOf(photoToUpdate.photo_file_path[preview.url_index]);
          if (index > -1) {
            filesToDelete.splice(index, 1);
          }
        } else if (!Array.isArray(photoToUpdate.photo_url)) {
          photo_urls.push(photoToUpdate.photo_url);
          photo_file_paths.push(photoToUpdate.photo_file_path);
          // Remove from delete list
          const index = filesToDelete.indexOf(photoToUpdate.photo_file_path);
          if (index > -1) {
            filesToDelete.splice(index, 1);
          }
        }
      }

      // Delete old files that were removed
      if (filesToDelete.length > 0) {
        await deletePhotosFromStorage(filesToDelete);
      }

      // 2. Upload new files
      if (updateSelectedFiles.length > 0) {
        setUpdateProgress(30);
        setUpdateMessage('Uploading new photos...');
        
        // Create FormData for file upload
        const formData = new FormData();
        updateSelectedFiles.forEach((file) => {
          formData.append('files', file);
        });
        formData.append('photoId', updatePhotoId);

        // Upload to R2
        const uploadResponse = await fetch('/api/file/photos', {
          method: 'POST',
          body: formData
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          if (uploadResponse.status === 401) {
            throw new Error(t('auth_error') || 'Authentication required');
          }
          throw new Error(errorData.error || t('upload_error'));
        }

        const uploadResult = await uploadResponse.json();
        
        // Get URLs and paths from R2 response
        const newPhotoUrls = uploadResult.photoUrls || [];
        const newFilePaths = uploadResult.filePaths || [];
        
        // Add new URLs and paths to the existing ones
        photo_urls.push(...newPhotoUrls);
        photo_file_paths.push(...newFilePaths);
      }

      // 3. Update the record via API
      setUpdateProgress(70);
      setUpdateMessage('Updating database record...');

      const updateResponse = await fetch('/api/knowledge/photos', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo_id: updatePhotoId,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
      });

      const updateResult = await updateResponse.json();

      if (!updateResponse.ok) {
        if (updateResponse.status === 401) {
          throw new Error(t('auth_error') || 'Authentication required');
        }
        throw new Error(updateResult.error || t('save_error').replace('{message}', 'Failed to update photo'));
      }

      // Update photo in local state instantly (no server refetch needed)
      const updatedPhoto = {
        id: photoToUpdate.id,
        photo_id: updatePhotoId,
        photo_url: photo_urls,
        photo_file_path: photo_file_paths,
        updated_at: new Date().toISOString()
      };
      updatePhotoInState(updatedPhoto);

      // Reset form
      setShowUpdateForm(false);
      setPhotoToUpdate(null);
      setUpdatePhotoId('');
      setUpdateSelectedFiles([]);
      setUpdateImagePreviews([]);

      // Show success message
      setUpdateProgress(100);
      setUpdateStatus('success');
      setUpdateMessage(t('photos_uploaded'));

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle');
      }, 1500);

    } catch (error) {
      console.error('Error updating photo:', error);
      setUpdateError(t('error_text') + ': ' + (error instanceof Error ? error.message : 'Unknown error'));

      // Show error in the progress overlay
      setUpdateStatus('error');
      setUpdateMessage(t('error_text') + ': ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to open image preview with all available images
  const openImagePreview = (images: string[], initialIndex: number = 0) => {

    // Ensure we're working with valid images
    const validImages = images.filter(url => url && url.trim() !== '');

    if (validImages.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    if (initialIndex >= validImages.length) {
      console.warn(`Initial index ${initialIndex} is out of bounds, resetting to 0`);
      initialIndex = 0;
    }

    setPreviewImages(validImages);
    setCurrentImageIndex(initialIndex);
    setPreviewImage(validImages[initialIndex]);
    // Zoom state is handled by ImageGalleryModal component // Reset zoom state

    // Pre-load all images to avoid flashing during navigation
    validImages.forEach((url) => {
      const img = new Image();
      img.src = url;
    });
  };

  // Navigate to previous image
  const showPreviousImage = useCallback(() => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  }, [previewImages, currentImageIndex]);

  // Navigate to next image
  const showNextImage = useCallback(() => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex + 1) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  }, [previewImages, currentImageIndex]);

  // Add keyboard navigation for photo gallery
  useEffect(() => {
    if (previewImage) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          showPreviousImage();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          showNextImage();
        } else if (event.key === 'Escape') {
          event.preventDefault();
          setPreviewImage(null);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [previewImage, showPreviousImage, showNextImage]);


  // Load initial data - only after client info is available
  useEffect(() => {
    if (clientLang) {
      // Clear any stale flags from sessionStorage
      sessionStorage.removeItem('showUploadFormAfterDiscard');
    }
  }, [clientLang]); // Remove refetchPhotos from dependencies

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (photoIdCheckTimeout) {
        clearTimeout(photoIdCheckTimeout);
      }
    };
  }, [photoIdCheckTimeout]);

  // Clean up sessionStorage when component unmounts
  useEffect(() => {
    return () => {
      sessionStorage.removeItem('showUploadFormAfterDiscard')
    }
  }, [])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard"
            titleKey="photo_page_title"
          />

          {/* Top Section */}
          <KnowledgeTopSection
            currentPath={pathname}
            totalFaqs={totalFaqs}
            totalFaqsLimit={totalFaqsLimit}
            photoCount={photoCount}
            photoLimit={photoLimit}
            isLoadingCount={isLoadingCount}
          />

          {/* Photo Gallery Content Section */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title ${themeConfig.text}`}>{t('photo_page_title')}</h2>
              <Button
                onClick={toggleUploadForm}
                variant="primary"
                size="sm"
                className="text-xs sm:text-base"
              >
                {showUploadForm ? t('cancel') : t('add_photos')}
              </Button>
            </div>

            {/* Help Section */}
            <div className="mb-6">
              {/* Description Text Above Buttons */}
              <p className={`${themeConfig.textSecondary} text-sm mb-2 font-body text-center`}>
                Please watch the video tutorial below if you have any questions.
              </p>
              
              {/* Buttons Row */}
              <div className="flex gap-3 sm:justify-center mb-4">
                {/* YouTube Guide Button */}
                <button
                  onClick={() => window.open('#', '_blank')} // TODO: Replace with actual YouTube URL
                  className={`flex items-center justify-center space-x-2 px-3 py-2 ${themeConfig.card} border-2 ${themeConfig.border} rounded-lg hover:${themeConfig.borderHover} transition-all duration-200 hover:scale-105 text-sm flex-1 sm:flex-none sm:w-auto`}
                  title="Watch the step-by-step tutorial"
                >
                  <FaYoutube className="w-4 h-4 text-red-500" />
                  <span className={themeConfig.textSecondary}>Video Tutorial</span>
                </button>
              </div>
            </div>

            {/* Upload Form */}
            {showUploadForm && (
              <div
                className={`relative mb-6 p-4 ${themeConfig.card} border ${themeConfig.border} rounded-2xl overflow-hidden`}
              >

                <div className="relative z-10">
                <h3 className={`text-sm font-semibold mb-3 font-title ${themeConfig.text}`}>{t('add_photos')}</h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Photo ID Input */}
                  <PhotoIdInput
                    photoId={photoId}
                    onPhotoIdChange={handlePhotoIdChange}
                    onPhotoIdBlur={handlePhotoIdBlur}
                    isCheckingPhotoId={isCheckingPhotoId}
                    photoIdExists={photoIdExists}
                    photoIdValidationMessage={photoIdValidationMessage}
                  />

                  {/* Photo Upload Area */}
                  <ImageUploadArea
                    isProcessingImage={isProcessingImage}
                    imagePreviews={imagePreviews}
                    selectedFiles={selectedFiles}
                    onFileChange={handleFileChange}
                    onRemoveFile={removeFile}
                    onAddMoreClick={() => document.getElementById('photo-file')?.click()}
                    uploadError={uploadError}
                    fileInputId="photo-file"
                  />

                  {/* Submit Button */}
                  <FormActions
                    onCancel={handleCancel}
                    isLoading={isUploading}
                    isDisabled={selectedFiles.length === 0 || !photoId.trim() || photoIdExists || isCheckingPhotoId}
                    loadingText={t('uploading')}
                    submitText={t('upload_photos')}
                  />
                </form>
                </div>
              </div>
            )}

            {/* Update Form */}
            {showUpdateForm && photoToUpdate && (
              <div
                className={`update-form-container relative mb-6 p-4 ${themeConfig.card} border ${themeConfig.border} rounded-2xl overflow-hidden`}
              >

                <div className="relative z-10">
                <h3 className={`text-sm font-semibold mb-3 font-title ${themeConfig.text}`}>{t('edit')} {t('photo_page_title')}</h3>
                <form onSubmit={showUpdateConfirmation} className="space-y-4">
                  {/* Photo ID Input - Read-only */}
                  <PhotoIdInput
                    photoId={updatePhotoId}
                    onPhotoIdChange={() => {}}
                    readOnly={true}
                  />

                  {/* Photo Upload Area */}
                  <ImageUploadArea
                    isProcessingImage={isProcessingImage}
                    imagePreviews={updateImagePreviews}
                    selectedFiles={updateSelectedFiles}
                    onFileChange={handleUpdateFileChange}
                    onRemoveFile={removeUpdateFile}
                    onAddMoreClick={() => document.getElementById('update-photo-file')?.click()}
                    uploadError={updateError}
                    fileInputId="update-photo-file"
                    emptyStateText={t('no_photos_added')}
                  />

                  {/* Submit Button */}
                  <FormActions
                    onCancel={handleCancelUpdate}
                    isLoading={isUpdating}
                    isDisabled={updateImagePreviews.length === 0 || !updatePhotoId.trim() || !hasUpdateFormChanges()}
                    loadingText={t('processing_image')}
                    submitText={t('update_photo')}
                  />
                </form>
                </div>
              </div>
            )}

            {/* Empty State - Show only when no photos */}
            {photoCount === 0 && (
              <div className={`text-center py-10 ${themeConfig.card} border ${themeConfig.border} rounded-lg mb-6`}>
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${themeConfig.textMuted} mb-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body`}>{t('no_photos_added')}</p>
                <p className={`${themeConfig.textMuted} text-[8px] sm:text-[10px] mt-1`}>{t('click_add_photos')}</p>
              </div>
            )}

            {/* Photo Catalog with Search */}
            {photoCount > 0 && (
              <div>
                {/* Search Bar - Only show when not loading */}
                {!isLoadingPhotos && (
                  <div className="mb-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder={t('search_photo_id')}
                        value={searchQuery}
                        onChange={handleSearchChange}
                        className={`w-full ${themeConfig.secondCard} border ${themeConfig.border} ${themeConfig.text} rounded-lg px-4 py-2 pl-10 text-base focus:outline-none ${themeConfig.borderActive} ${themeConfig.borderHover}`}

                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${themeConfig.textMuted}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}

                {/* Photo List */}
                {isLoadingPhotos ? (
                  <>
                    {/* Loading header */}
                    <div className="py-8 flex justify-center items-center">
                      <div className="flex flex-col items-center space-y-4">
                        {/* Main loading spinner */}
                        <div className="relative">
                          <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                          <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                        </div>

                        {/* Loading text with animation */}
                        <div className="text-center">
                          <p className={`${themeConfig.text} text-lg font-medium mb-1`}>{t('loading_photos')}</p>
                        </div>

                        {/* Progress indicator */}
                        <div className={`w-48 h-1 ${themeConfig.skeletonElement} rounded-full overflow-hidden`}>
                          <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* Skeleton loading for photo list */}
                    <div className="space-y-2">
                      {Array.from({ length: 6 }).map((_, index) => (
                        <div key={`skeleton-${index}`} className={`${themeConfig.card} border ${themeConfig.border} rounded-lg p-3 flex items-center`}>
                          {/* Skeleton thumbnail */}
                          <div className={`w-16 h-16 ${themeConfig.skeletonElement} rounded-lg animate-pulse`} style={{ animationDelay: `${index * 0.1}s` }}></div>

                          {/* Skeleton content */}
                          <div className="ml-3 flex-grow space-y-2">
                            <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse w-3/4`} style={{ animationDelay: `${index * 0.15}s` }}></div>
                            <div className={`h-3 ${themeConfig.skeletonElement} rounded animate-pulse w-1/2`} style={{ animationDelay: `${index * 0.2}s` }}></div>
                            <div className={`h-3 ${themeConfig.skeletonElement} rounded animate-pulse w-1/3`} style={{ animationDelay: `${index * 0.25}s` }}></div>
                          </div>

                          {/* Skeleton action buttons */}
                          <div className="flex space-x-2">
                            <div className={`w-8 h-8 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.3}s` }}></div>
                            <div className={`w-8 h-8 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.35}s` }}></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                ) : filteredPhotos.length > 0 ? (
                  <div>
                    {/* Vertical List Layout for Photos */}
                    <div className="space-y-2 mb-4">
                      {getCurrentPageItems().map((photo, index) => (
                        <div 
                          key={`photo-item-${photo.photo_id}-${index}`} 
                          className={`border ${themeConfig.border} ${themeConfig.borderHover} rounded-lg p-3 flex items-center`}
                        >
                          {/* Thumbnail - Optimized */}
                          <PhotoThumbnail
                            key={`photo-thumbnail-${photo.photo_id}-${index}`}
                            photo={{
                              photo_url: Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url],
                              photo_id: photo.photo_id
                            }}
                            className="w-16 h-16 rounded-lg border border-white/30"
                            onClick={() => {
                              // Open photo gallery
                              const urls = Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url];
                              openImagePreview(urls, 0);
                            }}
                          />

                          {/* Photo Info */}
                          <div className="ml-3 flex-grow">
                            <h3 className={`text-sm font-semibold ${themeConfig.textSecondary}`}>{photo.photo_id}</h3>
                            <p className={`text-xs ${themeConfig.textSecondary}`}>
                              {Array.isArray(photo.photo_url) ? photo.photo_url.length : 1} {t('photo_count')}
                            </p>
                            <p className={`text-[10px] ${themeConfig.textMuted}`}>
                              {new Date(photo.updated_at).toLocaleDateString()}
                            </p>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditClick(photo)}
                              className={`w-8 h-8 ${themeConfig.secondCard} border ${themeConfig.border} hover:border-jade-purple hover:${themeConfig.secondCard}`}
                              title={t('edit')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${themeConfig.textSecondary}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteClick(photo)}
                              className="w-8 h-8 bg-red-600 hover:bg-red-700 text-white"
                              title={t('delete')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Pagination Controls */}
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      itemsPerPage={itemsPerPage}
                      itemsPerPageOptions={[25, 50, 100]}
                      startIndex={(currentPage - 1) * itemsPerPage}
                      endIndex={Math.min(currentPage * itemsPerPage, filteredPhotos.length)}
                      totalItems={filteredPhotos.length}
                      onPageChange={handlePageClick}
                      onItemsPerPageChange={(newItemsPerPage) => {
                        setItemsPerPage(newItemsPerPage);
                        setCurrentPage(1);
                        // totalPages will auto-update via useMemo when itemsPerPage changes
                      }}
                      onPreviousPage={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      onNextPage={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    />
                  </div>
                ) : (
                  <div className={`text-center py-10 ${themeConfig.card} border ${themeConfig.border} rounded-lg`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${themeConfig.textMuted} mb-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body`}>{t('no_photos_found')}</p>
                    <p className={`${themeConfig.textMuted} text-[8px] sm:text-[10px] mt-1`}>{t('try_different_search')}</p>
                  </div>
                )}
              </div>
            )}
            </div>
          </div>
        </motion.div>
      </div>
      <DashboardFooter />

      {/* Cancel Confirmation Modal */}
      <CancelConfirmationModal
        showCancelConfirmation={showCancelConfirm}
        onKeepEditing={() => setShowCancelConfirm(false)}
        onConfirmDiscard={confirmCancel}
      />

      {/* Upload Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showUploadConfirm}
        onCancel={() => setShowUploadConfirm(false)}
        onConfirm={handleConfirmedUpload}
        title={t('upload_confirm').replace('{count}', selectedFiles.length.toString()).replace('{plural}', selectedFiles.length !== 1 ? 's' : '')}
        message={t('upload_confirm_message')
          .replace('{count}', selectedFiles.length.toString())
          .replace('{plural}', selectedFiles.length !== 1 ? 's' : '')
          .replace('{id}', photoId)}
      />

      {/* Status Overlay */}
      <UpdateStatusOverlay
        updateStatus={updateStatus}
        updateProgress={updateProgress}
        updateMessage={updateMessage}
        onClose={() => setUpdateStatus('idle')}
        loadingText={t('processing_upload')}
        completeText={t('complete_text')}
        successText={t('success_text')}
        errorText={t('error_text')}
      />

      {/* Cancel Update Confirmation Modal */}
      <CancelConfirmationModal
        showCancelConfirmation={showCancelUpdateConfirm}
        onKeepEditing={() => setShowCancelUpdateConfirm(false)}
        onConfirmDiscard={() => {
          setShowCancelUpdateConfirm(false);
          closeUpdateForm();
          const showUploadForm = sessionStorage.getItem('showUploadFormAfterDiscard');
          if (showUploadForm === 'true') {
            sessionStorage.removeItem('showUploadFormAfterDiscard');
            setTimeout(() => {
              setShowUploadForm(true);
            }, 100);
          }
        }}
      />

      {/* Update Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showUpdateConfirm}
        onCancel={() => setShowUpdateConfirm(false)}
        onConfirm={handleUpdateSubmit}
        title={t('update_photo_confirm')}
        message={t('update_photo_message')}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        deleteConfirm={showDeleteConfirm ? { id: photoToDelete?.id || 0 } : null}
        isDeleting={isDeleting}
        onCancel={cancelDelete}
        onConfirmDelete={confirmDeletePhoto}
      />

      {/* Remove Photo Confirmation Modal */}
      <PhotoRemoveConfirmationModal
        showRemovePhotoConfirm={showRemovePhotoConfirm}
        onCancel={cancelRemoveFile}
        onConfirm={confirmRemoveFile}
      />

      {/* Cannot Delete Photo Modal */}
      <PhotoCannotDeleteModal
        showCannotDeleteModal={showCannotDeleteModal}
        linkedQuestions={linkedQuestions}
        onClose={() => setShowCannotDeleteModal(false)}
      />

      {/* Image Gallery Modal */}
      <ImageGalleryModal
        imageGallery={previewImage ? {
          urls: previewImages,
          currentIndex: currentImageIndex
        } : null}
        onClose={() => setPreviewImage(null)}
        onImageChange={(index) => {
          setCurrentImageIndex(index);
          setPreviewImage(previewImages[index]);
        }}
        onPrevious={showPreviousImage}
        onNext={showNextImage}
      />
    </div>
  )
}